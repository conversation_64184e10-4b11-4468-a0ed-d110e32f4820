import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Input } from '@/components/ui/input.jsx'
import { Label } from '@/components/ui/label.jsx'
import { Badge } from '@/components/ui/badge.jsx'
import { Alert, AlertDescription } from '@/components/ui/alert.jsx'
import { Loader2, TrendingUp, TrendingDown, Activity, DollarSign, Users, BarChart3 } from 'lucide-react'
import './App.css'

function App() {
  const [tokenAddress, setTokenAddress] = useState('')
  const [prediction, setPrediction] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [apiHealth, setApiHealth] = useState(null)

  const API_BASE_URL = 'https://5000-ifm4salik07z1sa81gzqt-c661bd33.manusvm.computer/api'

  useEffect(() => {
    checkApiHealth()
  }, [])

  const checkApiHealth = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/health`)
      const data = await response.json()
      setApiHealth(data)
    } catch (err) {
      console.error('API health check failed:', err)
    }
  }

  const handlePredict = async () => {
    if (!tokenAddress.trim()) {
      setError('Please enter a token address')
      return
    }

    setLoading(true)
    setError('')
    setPrediction(null)

    try {
      const response = await fetch(`${API_BASE_URL}/predict`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token_address: tokenAddress.trim() }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Prediction failed')
      }

      const data = await response.json()
      setPrediction(data)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const formatNumber = (num) => {
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B'
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M'
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K'
    return num.toFixed(2)
  }

  const getMigrationStatus = (probability) => {
    if (probability >= 0.7) return { label: 'High', color: 'bg-green-500', icon: TrendingUp }
    if (probability >= 0.4) return { label: 'Medium', color: 'bg-yellow-500', icon: Activity }
    return { label: 'Low', color: 'bg-red-500', icon: TrendingDown }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            Pump.fun Token Migration Predictor
          </h1>
          <p className="text-slate-300 text-lg">
            AI-powered early detection system for token migrations to DEX
          </p>
          {apiHealth && (
            <Badge variant={apiHealth.status === 'healthy' ? 'default' : 'destructive'} className="mt-2">
              API Status: {apiHealth.status}
            </Badge>
          )}
        </div>

        {/* Input Section */}
        <Card className="mb-8 bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Token Analysis</CardTitle>
            <CardDescription className="text-slate-300">
              Enter a pump.fun token address to predict its migration probability
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="token-address" className="text-white">Token Address</Label>
              <Input
                id="token-address"
                placeholder="Enter token address (e.g., pvPDkwTiLLVGq5iSyPmfX4cF8yvnrKQ6R1m5ddNpump)"
                value={tokenAddress}
                onChange={(e) => setTokenAddress(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white placeholder-slate-400"
              />
            </div>
            <Button 
              onClick={handlePredict} 
              disabled={loading}
              className="w-full bg-purple-600 hover:bg-purple-700"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                'Predict Migration'
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert className="mb-8 bg-red-900/50 border-red-700">
            <AlertDescription className="text-red-200">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Prediction Results */}
        {prediction && (
          <div className="space-y-6">
            {/* Main Prediction Card */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Migration Prediction
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">
                      {(prediction.migration_probability * 100).toFixed(1)}%
                    </div>
                    <div className="text-slate-300">Migration Probability</div>
                    <div className="mt-2">
                      {(() => {
                        const status = getMigrationStatus(prediction.migration_probability)
                        const Icon = status.icon
                        return (
                          <Badge className={`${status.color} text-white`}>
                            <Icon className="mr-1 h-3 w-3" />
                            {status.label} Risk
                          </Badge>
                        )
                      })()}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">
                      {(prediction.confidence * 100).toFixed(1)}%
                    </div>
                    <div className="text-slate-300">Confidence</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">
                      {prediction.prediction === 1 ? 'YES' : 'NO'}
                    </div>
                    <div className="text-slate-300">Will Migrate</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Token Information */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Token Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-slate-700/50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="h-4 w-4 text-green-400" />
                      <span className="text-slate-300 text-sm">Market Cap</span>
                    </div>
                    <div className="text-white font-semibold">
                      ${formatNumber(prediction.token_info.market_cap_usd)}
                    </div>
                  </div>
                  <div className="bg-slate-700/50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Activity className="h-4 w-4 text-blue-400" />
                      <span className="text-slate-300 text-sm">Liquidity</span>
                    </div>
                    <div className="text-white font-semibold">
                      ${formatNumber(prediction.token_info.liquidity_usd)}
                    </div>
                  </div>
                  <div className="bg-slate-700/50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="h-4 w-4 text-purple-400" />
                      <span className="text-slate-300 text-sm">Holders</span>
                    </div>
                    <div className="text-white font-semibold">
                      {prediction.token_info.holders}
                    </div>
                  </div>
                  <div className="bg-slate-700/50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="text-xs">
                        {prediction.token_info.status}
                      </Badge>
                    </div>
                    <div className="text-white font-semibold text-sm">
                      {prediction.token_info.name} ({prediction.token_info.symbol})
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Key Features */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Key Analysis Features</CardTitle>
                <CardDescription className="text-slate-300">
                  Important metrics used in the prediction model
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="bg-slate-700/50 p-3 rounded">
                    <div className="text-slate-300 text-sm">Total Trades</div>
                    <div className="text-white font-semibold">{prediction.features_used.num_trades}</div>
                  </div>
                  <div className="bg-slate-700/50 p-3 rounded">
                    <div className="text-slate-300 text-sm">Unique Buyers</div>
                    <div className="text-white font-semibold">{prediction.features_used.num_unique_buyers}</div>
                  </div>
                  <div className="bg-slate-700/50 p-3 rounded">
                    <div className="text-slate-300 text-sm">Unique Sellers</div>
                    <div className="text-white font-semibold">{prediction.features_used.num_unique_sellers}</div>
                  </div>
                  <div className="bg-slate-700/50 p-3 rounded">
                    <div className="text-slate-300 text-sm">Trade Volume</div>
                    <div className="text-white font-semibold">${formatNumber(prediction.features_used.total_trade_volume)}</div>
                  </div>
                  <div className="bg-slate-700/50 p-3 rounded">
                    <div className="text-slate-300 text-sm">24h Volume</div>
                    <div className="text-white font-semibold">${formatNumber(prediction.features_used.volume_24h)}</div>
                  </div>
                  <div className="bg-slate-700/50 p-3 rounded">
                    <div className="text-slate-300 text-sm">Has Socials</div>
                    <div className="text-white font-semibold">{prediction.features_used.hasSocials ? 'Yes' : 'No'}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Footer */}
        <div className="text-center mt-12 text-slate-400 text-sm">
          <p>Powered by advanced machine learning algorithms and real-time blockchain data</p>
          <p className="mt-1">⚠️ This is for educational purposes only. Not financial advice.</p>
        </div>
      </div>
    </div>
  )
}

export default App

