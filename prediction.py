import os
import sys
import joblib
import pandas as pd
import numpy as np
from datetime import datetime
import json
import requests
import time

# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Blueprint, request, jsonify

prediction_bp = Blueprint('prediction', __name__)

# Load the trained model and scaler
MODEL_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'pumpfun_migration_model.joblib')
SCALER_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'pumpfun_migration_scaler.joblib')

try:
    model = joblib.load(MODEL_PATH)
    scaler = joblib.load(SCALER_PATH)
    print(f"Model and scaler loaded successfully from {MODEL_PATH} and {SCALER_PATH}")
except Exception as e:
    print(f"Error loading model or scaler: {e}")
    model = None
    scaler = None

class SolanaTrackerAPI:
    def __init__(self, api_key=None):
        self.base_url = "https://data.solanatracker.io"
        self.headers = {"x-api-key": api_key} if api_key else {}

    def _make_request(self, endpoint, params=None, max_retries=3, backoff_factor=2):
        url = f"{self.base_url}/{endpoint}"
        for i in range(max_retries):
            try:
                response = requests.get(url, headers=self.headers, params=params)
                response.raise_for_status()
                return response.json()
            except requests.exceptions.RequestException as e:
                print(f"Error making request to {url} (attempt {i+1}/{max_retries}): {e}")
                if response is not None and response.status_code == 429:
                    sleep_time = backoff_factor ** i
                    print(f"Rate limit hit. Retrying in {sleep_time} seconds...")
                    time.sleep(sleep_time)
                else:
                    break
        return None

    def get_token_data(self, token_address):
        return self._make_request(f"token/{token_address}")

    def get_trades(self, token_address, limit=100, cursor=None):
        params = {"limit": limit}
        if cursor:
            params["cursor"] = cursor
        return self._make_request(f"trades/{token_address}", params)

def extract_features_from_token_data(token_data, trades_data):
    """Extract features from token data and trades data for prediction"""
    features = {}
    
    # Basic token features
    features['decimals'] = token_data.get('decimals', 6)
    features['hasSocials'] = 1 if token_data.get('hasSocials', False) else 0
    features['liquidityUsd'] = token_data.get('liquidityUsd', 0)
    features['marketCapUsd'] = token_data.get('marketCapUsd', 0)
    features['priceUsd'] = token_data.get('priceUsd', 0)
    features['lpBurn'] = token_data.get('lpBurn', 0)
    features['holders'] = token_data.get('holders', 0)
    features['buys'] = token_data.get('buys', 0)
    features['sells'] = token_data.get('sells', 0)
    features['totalTransactions'] = token_data.get('totalTransactions', 0)
    features['volume'] = token_data.get('volume', 0)
    features['volume_5m'] = token_data.get('volume_5m', 0)
    features['volume_15m'] = token_data.get('volume_15m', 0)
    features['volume_30m'] = token_data.get('volume_30m', 0)
    features['volume_1h'] = token_data.get('volume_1h', 0)
    features['volume_6h'] = token_data.get('volume_6h', 0)
    features['volume_12h'] = token_data.get('volume_12h', 0)
    features['volume_24h'] = token_data.get('volume_24h', 0)
    
    # Time-based features
    created_at = token_data.get('createdAt', 0)
    if created_at:
        current_time = int(time.time() * 1000)  # Current time in milliseconds
        features['createdAt'] = created_at
        features['lastUpdated'] = token_data.get('lastUpdated', current_time)
    else:
        features['createdAt'] = 0
        features['lastUpdated'] = 0
    
    # Trade-based features
    trades = trades_data.get('data', []) if trades_data else []
    features['num_trades'] = len(trades)
    features['total_trade_volume'] = sum(trade.get('amount', 0) for trade in trades)
    
    # Unique buyers and sellers
    buyers = set()
    sellers = set()
    for trade in trades:
        if 'buyer' in trade:
            buyers.add(trade['buyer'])
        if 'seller' in trade:
            sellers.add(trade['seller'])
    
    features['num_unique_buyers'] = len(buyers)
    features['num_unique_sellers'] = len(sellers)
    
    # Creator-specific features (simplified for now)
    creator_address = token_data.get('deployer', '')
    creator_buy_volume = 0
    creator_sell_volume = 0
    creator_first_trade_buy_amount = 0
    creator_first_trade_sell_amount = 0
    creator_last_trade_buy_amount = 0
    creator_last_trade_sell_amount = 0
    
    for trade in trades:
        if trade.get('owner') == creator_address:
            if trade.get('type') == 'buy':
                creator_buy_volume += trade.get('amount', 0)
                if creator_first_trade_buy_amount == 0:
                    creator_first_trade_buy_amount = trade.get('amount', 0)
                creator_last_trade_buy_amount = trade.get('amount', 0)
            elif trade.get('type') == 'sell':
                creator_sell_volume += trade.get('amount', 0)
                if creator_first_trade_sell_amount == 0:
                    creator_first_trade_sell_amount = trade.get('amount', 0)
                creator_last_trade_sell_amount = trade.get('amount', 0)
    
    features['creator_first_trade_buy_amount'] = creator_first_trade_buy_amount
    features['creator_first_trade_sell_amount'] = creator_first_trade_sell_amount
    features['creator_last_trade_buy_amount'] = creator_last_trade_buy_amount
    features['creator_last_trade_sell_amount'] = creator_last_trade_sell_amount
    features['creator_total_buy_volume'] = creator_buy_volume
    features['creator_total_sell_volume'] = creator_sell_volume
    features['creator_net_volume'] = creator_buy_volume - creator_sell_volume
    
    # Market feature (one-hot encoded)
    features['market_pumpfun'] = 1  # Assuming all tokens are from pumpfun
    
    return features

@prediction_bp.route('/predict', methods=['POST'])
def predict_migration():
    """Predict if a token will migrate based on its address"""
    if not model or not scaler:
        return jsonify({'error': 'Model not loaded'}), 500
    
    data = request.get_json()
    token_address = data.get('token_address')
    
    if not token_address:
        return jsonify({'error': 'token_address is required'}), 400
    
    # Initialize API client
    api_key = "cfb8683b-785c-45d4-b3cd-8ab0b2b0c4d7"  # You might want to move this to environment variables
    solana_tracker = SolanaTrackerAPI(api_key)
    
    try:
        # Fetch token data
        token_data = solana_tracker.get_token_data(token_address)
        if not token_data:
            return jsonify({'error': 'Failed to fetch token data'}), 400
        
        # Fetch trades data
        trades_data = solana_tracker.get_trades(token_address, limit=100)
        
        # Extract features
        features = extract_features_from_token_data(token_data, trades_data)
        
        # Convert to DataFrame with the same column order as training
        feature_columns = [
            'decimals', 'hasSocials', 'liquidityUsd', 'marketCapUsd', 'priceUsd', 'lpBurn',
            'createdAt', 'lastUpdated', 'holders', 'buys', 'sells', 'totalTransactions',
            'volume', 'volume_5m', 'volume_15m', 'volume_30m', 'volume_1h', 'volume_6h',
            'volume_12h', 'volume_24h', 'num_trades', 'total_trade_volume',
            'num_unique_buyers', 'num_unique_sellers', 'creator_first_trade_buy_amount',
            'creator_first_trade_sell_amount', 'creator_last_trade_buy_amount',
            'creator_last_trade_sell_amount', 'creator_total_buy_volume',
            'creator_total_sell_volume', 'creator_net_volume', 'market_pumpfun'
        ]
        
        # Create feature vector
        feature_vector = [features.get(col, 0) for col in feature_columns]
        feature_df = pd.DataFrame([feature_vector], columns=feature_columns)
        
        # Scale features
        feature_scaled = scaler.transform(feature_df)
        
        # Make prediction
        prediction = model.predict(feature_scaled)[0]
        prediction_proba = model.predict_proba(feature_scaled)[0]
        
        # Prepare response
        response = {
            'token_address': token_address,
            'prediction': int(prediction),
            'migration_probability': float(prediction_proba[1]),
            'confidence': float(max(prediction_proba)),
            'token_info': {
                'name': token_data.get('name', ''),
                'symbol': token_data.get('symbol', ''),
                'market_cap_usd': token_data.get('marketCapUsd', 0),
                'liquidity_usd': token_data.get('liquidityUsd', 0),
                'holders': token_data.get('holders', 0),
                'status': token_data.get('status', 'unknown')
            },
            'features_used': features,
            'timestamp': datetime.now().isoformat()
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({'error': f'Prediction failed: {str(e)}'}), 500

@prediction_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'model_loaded': model is not None,
        'scaler_loaded': scaler is not None,
        'timestamp': datetime.now().isoformat()
    })

@prediction_bp.route('/model_info', methods=['GET'])
def model_info():
    """Get information about the loaded model"""
    if not model:
        return jsonify({'error': 'Model not loaded'}), 500
    
    return jsonify({
        'model_type': type(model).__name__,
        'n_estimators': getattr(model, 'n_estimators', None),
        'learning_rate': getattr(model, 'learning_rate', None),
        'max_depth': getattr(model, 'max_depth', None),
        'feature_importances': model.feature_importances_.tolist() if hasattr(model, 'feature_importances_') else None
    })

