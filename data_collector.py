
import requests
import pandas as pd
import time
import json
import os

class SolanaTrackerAPI:
    def __init__(self, api_key=None):
        self.base_url = "https://data.solanatracker.io"
        self.headers = {"x-api-key": api_key} if api_key else {}

    def _make_request(self, endpoint, params=None, max_retries=10, backoff_factor=5):
        url = f"{self.base_url}/{endpoint}"
        for i in range(max_retries):
            try:
                response = requests.get(url, headers=self.headers, params=params)
                response.raise_for_status()  # Raise an exception for HTTP errors
                return response.json()
            except requests.exceptions.RequestException as e:
                print(f"Error making request to {url} (attempt {i+1}/{max_retries}): {e}")
                if response is not None and response.status_code == 429:
                    sleep_time = backoff_factor ** i + 1 # Add 1 second to ensure at least 1 second sleep
                    print(f"Rate limit hit. Retrying in {sleep_time} seconds...")
                    time.sleep(sleep_time)
                else:
                    break # For other errors, don\'t retry
        return None

    def get_token_data(self, token_address):
        return self._make_request(f"token/{token_address}")

    def search_tokens(self, query=None, market=None, limit=100, cursor=None, status=None):
        params = {"limit": limit}
        if query: 
            params["query"] = query
        if market: 
            params["market"] = market
        if cursor: 
            params["cursor"] = cursor
        if status: # Add status parameter for filtering
            params["status"] = status
        return self._make_request("search", params)

    def get_tokens_by_deployer(self, deployer_address, limit=100, cursor=None):
        params = {"limit": limit}
        if cursor: 
            params["cursor"] = cursor
        return self._make_request(f"deployer/{deployer_address}", params)

    def get_market_data(self, token_address):
        return self._make_request(f"token/{token_address}/market")

    def get_ohlcv_data(self, token_address, timeframe="1H", start_time=None, end_time=None):
        params = {"timeframe": timeframe}
        if start_time: 
            params["startTime"] = start_time
        if end_time: 
            params["endTime"] = end_time
        return self._make_request(f"token/{token_address}/ohlcv", params)

    def get_trades(self, token_address, limit=100, cursor=None):
        params = {"limit": limit}
        if cursor: 
            params["cursor"] = cursor
        return self._make_request(f"trades/{token_address}", params)


# --- Data Collection Script ---

def fetch_detailed_token_data(solana_tracker_api, token_info):
    token_address = token_info["mint"]
    detailed_data = token_info.copy()

    # Fetch trades
    trades_data = solana_tracker_api.get_trades(token_address, limit=100) 
    if trades_data and trades_data.get("data"):
        detailed_data["trades"] = trades_data["data"]
    else:
        detailed_data["trades"] = []

    time.sleep(2) # Increased delay between detailed calls

    return detailed_data

def collect_pumpfun_token_data(api_key=None, output_file="pumpfun_tokens_detailed.csv", max_tokens=1000):
    solana_tracker = SolanaTrackerAPI(api_key)
    all_pumpfun_tokens_raw = []
    all_detailed_tokens = []
    cursor = None
    tokens_collected = 0

    # Resume collection if a partial file exists
    if os.path.exists(output_file):
        try:
            df_existing = pd.read_csv(output_file)
            all_detailed_tokens = df_existing.to_dict(orient='records')
            tokens_collected = len(all_detailed_tokens)
            print(f"Resuming collection. Already collected {tokens_collected} tokens.")
            # Find the last cursor if possible to resume initial search
            if not df_existing.empty and 'id' in df_existing.columns:
                last_token_id = df_existing['id'].iloc[-1]
                # This is a simplification. A proper resume would need to store the cursor for the search endpoint.
                # For now, we'll just start the search from the beginning and skip already collected tokens.
        except Exception as e:
            print(f"Error loading existing data: {e}. Starting fresh.")
            all_detailed_tokens = []
            tokens_collected = 0

    print("Starting initial collection of pump.fun tokens...")
    while tokens_collected < max_tokens:
        print(f"Collecting batch of tokens (collected so far: {tokens_collected})...")
        # We need to ensure we don't re-fetch tokens already collected if resuming
        response = solana_tracker.search_tokens(market="pumpfun", limit=100, cursor=cursor)
        if response and response.get("data"):
            new_tokens_found = 0
            for token in response["data"]:
                # Simple check to avoid re-processing already collected tokens (by mint address)
                if not any(d.get('mint') == token.get('mint') for d in all_detailed_tokens):
                    all_pumpfun_tokens_raw.append(token)
                    new_tokens_found += 1
                
                if (tokens_collected + new_tokens_found) >= max_tokens:
                    break

            tokens_collected += new_tokens_found

            if response.get("hasNextPage"):
                cursor = response["nextCursor"]
                time.sleep(5)  # Increased delay for search API to 5 seconds
            else:
                break
        else:
            print("No more tokens or error in initial search response.")
            break

    print(f"Initial collection complete. Found {len(all_pumpfun_tokens_raw)} new pump.fun tokens to process. Now fetching detailed data...")

    # Now, fetch detailed data for each new token and append to existing
    for i, token_info in enumerate(all_pumpfun_tokens_raw):
        if (len(all_detailed_tokens) + i) >= max_tokens: # Ensure we don't exceed max_tokens
            break
        token_display_name = token_info.get("symbol", token_info.get("name", "Unknown"))
        print(f"Fetching detailed data for token {len(all_detailed_tokens) + i + 1}/{max_tokens}: {token_display_name}")
        detailed_token = fetch_detailed_token_data(solana_tracker, token_info)
        if detailed_token:
            all_detailed_tokens.append(detailed_token)
            # Save progress periodically
            if (len(all_detailed_tokens) % 50 == 0) or (len(all_detailed_tokens) == max_tokens):
                df_progress = pd.DataFrame(all_detailed_tokens)
                df_progress.to_csv(output_file, index=False)
                print(f"Saved {len(all_detailed_tokens)} tokens to {output_file}")

    # Final save
    df = pd.DataFrame(all_detailed_tokens)
    df.to_csv(output_file, index=False)
    print(f"Collected {len(all_detailed_tokens)} detailed pump.fun tokens and saved to {output_file}")
    return output_file

if __name__ == "__main__":
    SOLANATRACKER_API_KEY = "cfb8683b-785c-45d4-b3cd-8ab0b2b0c4d7"
    
    # Collect up to 1000 pump.fun tokens with detailed data
    collected_file = collect_pumpfun_token_data(api_key=SOLANATRACKER_API_KEY, max_tokens=1000)
    
    # Example of reading the collected data
    # df_tokens = pd.read_csv(collected_file)
    # print(df_tokens.head())


