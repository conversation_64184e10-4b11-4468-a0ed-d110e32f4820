
import pandas as pd
import json

def feature_engineer_and_preprocess(input_file="pumpfun_tokens_detailed.csv", output_file="pumpfun_tokens_features.csv"):
    df = pd.read_csv(input_file)

    # Convert 'trades' column from string to list of dictionaries
    # Handle potential NaN values in 'trades' column
    df["trades"] = df["trades"].apply(lambda x: json.loads(x) if pd.notna(x) and x != "[]" else [])

    # Initialize new feature columns
    df["num_trades"] = 0
    df["total_trade_volume"] = 0.0
    df["num_unique_buyers"] = 0
    df["num_unique_sellers"] = 0
    df["creator_first_trade_buy_amount"] = 0.0
    df["creator_first_trade_sell_amount"] = 0.0
    df["creator_last_trade_buy_amount"] = 0.0
    df["creator_last_trade_sell_amount"] = 0.0
    df["creator_total_buy_volume"] = 0.0
    df["creator_total_sell_volume"] = 0.0
    df["creator_net_volume"] = 0.0

    # Iterate through each token to extract trade-related features
    for index, row in df.iterrows():
        trades = row["trades"]
        if trades:
            df.at[index, "num_trades"] = len(trades)
            
            total_volume = 0.0
            buyers = set()
            sellers = set()
            
            creator_address = row["tokenDetails"].get("creator") if isinstance(row["tokenDetails"], dict) else json.loads(row["tokenDetails"]).get("creator")
            creator_buy_volume = 0.0
            creator_sell_volume = 0.0
            
            first_trade_processed = False
            last_trade_processed = False

            # Process trades in reverse to easily find last trade, then iterate normally for others
            for trade in reversed(trades):
                if not last_trade_processed and trade["owner"] == creator_address:
                    if trade["type"] == "buy":
                        df.at[index, "creator_last_trade_buy_amount"] = trade["amount"]
                    elif trade["type"] == "sell":
                        df.at[index, "creator_last_trade_sell_amount"] = trade["amount"]
                    last_trade_processed = True
                if last_trade_processed and first_trade_processed: # Optimization: stop if both first and last creator trades are found
                    break

            for trade in trades:
                total_volume += trade["amount"]
                buyers.add(trade["buyer"])
                sellers.add(trade["seller"])

                if trade["owner"] == creator_address:
                    if trade["type"] == "buy":
                        creator_buy_volume += trade["amount"]
                        if not first_trade_processed:
                            df.at[index, "creator_first_trade_buy_amount"] = trade["amount"]
                    elif trade["type"] == "sell":
                        creator_sell_volume += trade["amount"]
                        if not first_trade_processed:
                            df.at[index, "creator_first_trade_sell_amount"] = trade["amount"]
                    first_trade_processed = True

            df.at[index, "total_trade_volume"] = total_volume
            df.at[index, "num_unique_buyers"] = len(buyers)
            df.at[index, "num_unique_sellers"] = len(sellers)
            df.at[index, "creator_total_buy_volume"] = creator_buy_volume
            df.at[index, "creator_total_sell_volume"] = creator_sell_volume
            df.at[index, "creator_net_volume"] = creator_buy_volume - creator_sell_volume

    # Feature: is_migrated (Target variable)
    df["is_migrated"] = df["status"].apply(lambda x: 1 if x == "graduating" else 0)

    # Drop columns that are not needed for ML or are redundant after feature engineering
    columns_to_drop = ["trades", "tokenDetails", "status", "image", "poolAddress", "quoteToken", "freezeAuthority", "mintAuthority"]
    df = df.drop(columns=[col for col in columns_to_drop if col in df.columns])

    # Handle missing values (simple imputation for now, can be improved)
    for col in df.columns:
        if df[col].dtype == "object":
            df[col] = df[col].fillna("unknown")
        else:
            df[col] = df[col].fillna(0) # Fill numerical NaNs with 0 or mean/median

    # Save the processed data
    df.to_csv(output_file, index=False)
    print(f"Feature engineering and preprocessing complete. Processed data saved to {output_file}")
    return output_file

if __name__ == "__main__":
    feature_engineer_and_preprocess()


