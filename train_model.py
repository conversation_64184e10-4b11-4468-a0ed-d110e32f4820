
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score, precision_score, recall_score, f1_score
from sklearn.preprocessing import StandardScaler
import joblib
import numpy as np

def train_and_evaluate_model(input_file="pumpfun_tokens_features.csv", model_output_file="pumpfun_migration_model.joblib"):
    df = pd.read_csv(input_file)

    # Check the distribution of the target variable
    print("\nDistribution of \'is_migrated\' before splitting:")
    print(df["is_migrated"].value_counts())

    # Identify target variable and features
    target = "is_migrated"
    # Exclude non-numeric or identifier columns that are not features
    features = [col for col in df.columns if col not in [target, "id", "name", "symbol", "mint", "deployer"]]

    # Handle categorical features using one-hot encoding
    categorical_cols = df[features].select_dtypes(include=["object"]).columns
    if not categorical_cols.empty:
        print(f"Categorical columns found: {categorical_cols.tolist()}. Applying one-hot encoding.")
        df = pd.get_dummies(df, columns=categorical_cols, drop_first=True)
        # Update features list after one-hot encoding
        features = [col for col in df.columns if col not in [target, "id", "name", "symbol", "mint", "deployer"]]

    X = df[features]
    y = df[target]

    # Handle potential NaN values that might have been introduced or missed
    X = X.fillna(0) # Simple imputation for numerical NaNs

    # Drop columns that became all NaN after one-hot encoding if any (e.g., if a category was only in one row that was dropped)
    X = X.dropna(axis=1, how='all')

    # Align columns after one-hot encoding, especially if some categories are missing in test set
    # This is more critical in deployment, but good practice to consider here.
    # For now, assuming train/test split will handle this if data is from same source.

    # Split data into training and testing sets
    # Removed stratify=y due to the ValueError from previous run (too few samples in minority class)
    # This means the test set might not have any \'migrated\' tokens, or very few.
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    print("\nDistribution of \'is_migrated\' in training set:")
    print(y_train.value_counts())
    print("\nDistribution of \'is_migrated\' in test set:")
    print(y_test.value_counts())

    # Scale numerical features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # Train a Gradient Boosting Classifier
    print("\nTraining Gradient Boosting Classifier...")
    model = GradientBoostingClassifier(n_estimators=100, learning_rate=0.1, max_depth=3, random_state=42)
    model.fit(X_train_scaled, y_train)

    # Evaluate the model
    y_pred = model.predict(X_test_scaled)
    
    # Check if y_test contains both classes before calculating ROC AUC
    if len(np.unique(y_test)) > 1:
        y_proba = model.predict_proba(X_test_scaled)[:, 1] # Probability of being migrated
        roc_auc = roc_auc_score(y_test, y_proba)
    else:
        roc_auc = np.nan # Not applicable if only one class is present in y_test
        print("Warning: ROC AUC cannot be calculated as the test set contains only one class.")

    print("\nModel Evaluation:")
    print(f"Accuracy: {accuracy_score(y_test, y_pred):.4f}")
    if not np.isnan(roc_auc):
        print(f"ROC AUC: {roc_auc:.4f}")
    print("Precision: {0:.4f}".format(precision_score(y_test, y_pred, zero_division=0)))
    print("Recall: {0:.4f}".format(recall_score(y_test, y_pred, zero_division=0)))
    print("F1-Score: {0:.4f}".format(f1_score(y_test, y_pred, zero_division=0)))
    print("Classification Report:")
    print(classification_report(y_test, y_pred, zero_division=0))

    # Save the trained model and scaler
    joblib.dump(model, model_output_file)
    joblib.dump(scaler, "pumpfun_migration_scaler.joblib")
    print(f"Trained model saved to {model_output_file}")
    print(f"Scaler saved to pumpfun_migration_scaler.joblib")

if __name__ == "__main__":
    train_and_evaluate_model()


