
import pandas as pd
import json
import ast # Import the ast module

def load_and_preprocess_data(file_path="pumpfun_tokens.csv"):
    df = pd.read_csv(file_path)

    # Convert relevant columns to datetime
    df["createdAt"] = pd.to_datetime(df["createdAt"], unit="ms")
    df["lastUpdated"] = pd.to_datetime(df["lastUpdated"], unit="ms")

    # Extract creator from tokenDetails (string representation of dict)
    # Use ast.literal_eval to safely parse the string as a Python literal
    df["creator"] = df["tokenDetails"].apply(lambda x: ast.literal_eval(x).get("creator") if pd.notna(x) else None)

    # Define target variable: 1 if "graduating", 0 otherwise
    df["is_migrated"] = (df["status"] == "graduating").astype(int)

    # Calculate time since creation (in hours)
    df["age_hours"] = (df["lastUpdated"] - df["createdAt"]).dt.total_seconds() / 3600

    # Calculate buy/sell ratio
    df["buy_sell_ratio"] = df["buys"] / df["sells"].replace(0, 1) # Avoid division by zero

    # Select initial features for analysis
    features = [
        "liquidityUsd",
        "marketCapUsd",
        "priceUsd",
        "lpBurn",
        "buys",
        "sells",
        "totalTransactions",
        "volume",
        "volume_5m",
        "volume_15m",
        "volume_30m",
        "volume_1h",
        "volume_6h",
        "volume_12h",
        "volume_24h",
        "hasSocials",
        "age_hours",
        "buy_sell_ratio",
        "creator",
        "is_migrated"
    ]

    df_processed = df[features].copy()

    # Handle missing values (simple imputation for now, more advanced methods later)
    for col in ["liquidityUsd", "marketCapUsd", "priceUsd", "volume"]:
        df_processed[col] = df_processed[col].fillna(0) # Fill numerical NaNs with 0 or mean/median
    
    # Fill NaN in buy_sell_ratio with 0 if sells was 0, or 1 if both buys and sells were 0 (no activity)
    df_processed["buy_sell_ratio"] = df_processed["buy_sell_ratio"].fillna(0)

    # For creator-centric analysis, we need to aggregate data by creator.
    # This will be done in a separate step or as part of feature engineering.

    print("Data loaded and initial preprocessing complete.")
    print(df_processed.head())
    print(df_processed.info())

    return df_processed

if __name__ == "__main__":
    processed_data = load_and_preprocess_data()
    # You can save the processed data if needed
    # processed_data.to_csv("processed_pumpfun_tokens.csv", index=False)


